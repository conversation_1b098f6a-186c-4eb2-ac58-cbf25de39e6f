<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Column Validation</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
</head>
<body>
    <h1>Test Column Validation</h1>
    <button onclick="testValidation()">Test Validation Logic</button>
    <div id="results"></div>

    <script>
        // Copy the required columns and validation functions from script.js
        const requiredColumns = [
            'Transaction ID',
            'Trans Ref No',
            'Customer Id',
            'Customer Name',
            'Account No',
            'Account Open Date',
            'Product Type',
            'Branch',
            'Date',
            'Tran Amount',
            'Tran Currency',
            'Dr or Cr',
            'Counter Party Name',
            'Counter Customer ID',
            'Counter Account No',
            'Remarks',
            'Particulars',
            'Transaction Location Id',
            'Approved User Id',
            'Entry User Id',
            'Posted User Id'
        ];

        function validateColumns(headers) {
            const missingColumns = [];
            const normalizedHeaders = headers.map(h => h ? h.toString().toLowerCase().trim() : '');
            
            // Check if we have enough columns
            if (headers.length < requiredColumns.length) {
                throw new Error(`File must contain at least ${requiredColumns.length} columns. Found ${headers.length} columns.`);
            }
            
            // First, try exact positional matching (preferred)
            let exactMatch = true;
            for (let i = 0; i < requiredColumns.length; i++) {
                const requiredCol = requiredColumns[i].toLowerCase();
                const headerCol = normalizedHeaders[i];
                
                if (headerCol !== requiredCol) {
                    exactMatch = false;
                    break;
                }
            }
            
            if (exactMatch) {
                return []; // Perfect match, no missing columns
            }
            
            // If exact positional match fails, check if all required columns exist anywhere
            const foundColumns = new Set();
            
            for (const requiredCol of requiredColumns) {
                const normalizedRequired = requiredCol.toLowerCase();
                let found = false;
                
                for (let i = 0; i < normalizedHeaders.length; i++) {
                    if (normalizedHeaders[i] === normalizedRequired) {
                        foundColumns.add(requiredCol);
                        found = true;
                        break;
                    }
                }
                
                if (!found) {
                    missingColumns.push(`"${requiredCol}"`);
                }
            }
            
            // If we found all columns but not in the right order, provide helpful message
            if (missingColumns.length === 0 && !exactMatch) {
                console.warn('All required columns found but not in the expected order. Processing will continue but column order is recommended for best results.');
                
                // Reorder the data to match expected column order
                return []; // Allow processing to continue
            }
            
            return missingColumns;
        }

        function testValidation() {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = '<h2>Test Results:</h2>';

            // Test 1: Perfect match
            console.log('Test 1: Perfect match');
            const perfectHeaders = [...requiredColumns];
            const result1 = validateColumns(perfectHeaders);
            resultsDiv.innerHTML += `<p><strong>Test 1 (Perfect match):</strong> ${result1.length === 0 ? 'PASS' : 'FAIL'} - Missing: ${result1.join(', ')}</p>`;

            // Test 2: Case insensitive match
            console.log('Test 2: Case insensitive match');
            const caseHeaders = requiredColumns.map(col => col.toUpperCase());
            const result2 = validateColumns(caseHeaders);
            resultsDiv.innerHTML += `<p><strong>Test 2 (Case insensitive):</strong> ${result2.length === 0 ? 'PASS' : 'FAIL'} - Missing: ${result2.join(', ')}</p>`;

            // Test 3: Missing columns
            console.log('Test 3: Missing columns');
            const missingHeaders = requiredColumns.slice(0, 10); // Only first 10 columns
            try {
                const result3 = validateColumns(missingHeaders);
                resultsDiv.innerHTML += `<p><strong>Test 3 (Missing columns):</strong> Should have failed but didn't</p>`;
            } catch (error) {
                resultsDiv.innerHTML += `<p><strong>Test 3 (Missing columns):</strong> PASS - Correctly detected insufficient columns</p>`;
            }

            // Test 4: All columns present but wrong order
            console.log('Test 4: Wrong order');
            const shuffledHeaders = [...requiredColumns].reverse(); // Reverse order
            const result4 = validateColumns(shuffledHeaders);
            resultsDiv.innerHTML += `<p><strong>Test 4 (Wrong order):</strong> ${result4.length === 0 ? 'PASS' : 'FAIL'} - Missing: ${result4.join(', ')}</p>`;

            // Test 5: Extra columns
            console.log('Test 5: Extra columns');
            const extraHeaders = [...requiredColumns, 'Extra Column 1', 'Extra Column 2'];
            const result5 = validateColumns(extraHeaders);
            resultsDiv.innerHTML += `<p><strong>Test 5 (Extra columns):</strong> ${result5.length === 0 ? 'PASS' : 'FAIL'} - Missing: ${result5.join(', ')}</p>`;
        }
    </script>
</body>
</html>
